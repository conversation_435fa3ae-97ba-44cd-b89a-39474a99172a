import { defineStore } from 'pinia'

export const useUserStore = defineStore('user', {
  state: () => ({
    // 用户信息
    user: null,
    // 认证token
    token: null,
    // 是否已登录
    isLoggedIn: false
  }),

  getters: {
    // 获取用户名
    username: (state) => state.user?.username || '',
    // 获取用户ID
    userId: (state) => state.user?.id || null,
    // 检查是否已认证
    isAuthenticated: (state) => !!state.token && !!state.user
  },

  actions: {
    // 设置用户信息
    setUser(userData) {
      this.user = userData
      this.token = userData.token
      this.isLoggedIn = true
      
      // 同步到localStorage
      if (userData.token) {
        localStorage.setItem('token', userData.token)
      }
      if (userData.username) {
        localStorage.setItem('username', userData.username)
      }
    },

    // 清除用户信息（登出）
    clearUser() {
      this.user = null
      this.token = null
      this.isLoggedIn = false
      
      // 清除localStorage
      localStorage.removeItem('token')
      localStorage.removeItem('username')
    },

    // 从localStorage恢复用户状态
    restoreUserFromStorage() {
      const token = localStorage.getItem('token')
      const username = localStorage.getItem('username')
      
      if (token && username) {
        this.setUser({
          username,
          token
        })
      }
    },

    // 更新用户信息
    updateUser(userData) {
      if (this.user) {
        this.user = { ...this.user, ...userData }
      }
    },

    // 登出
    logout() {
      this.clearUser()
    }
  },

  persist: {
    enabled: true,
    strategies: [
      {
        storage: localStorage,
        paths: ['user', 'token', 'isLoggedIn']
      }
    ]
  }
})
